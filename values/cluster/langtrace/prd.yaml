# Langtrace configuration for PRODUCTION environment

istio-ingress:
  enabled: true
  istio:
    ingress:
      langtrace:
        enabled: true
        gateways:
          - common
        hosts:
          - "langtrace.gcp-prd.inspectorio.com"
        http:
          - route:
              - destination:
                  host: langtrace-svc.default.svc.cluster.local
                  port:
                    number: 3000

langtrace:
  langtraceApp:
    name: langtrace
    image: "asia-docker.pkg.dev/inspectorio-ant/mirror/scale3labs/langtrace-client"  # Use mirrored image for prod
    langtrace_release: "v1.0.0"  # Use specific version for production
    replicaCount: 3  # Higher replica count for production
    
  postgres:
    enabled: true
    storageSize: 200Gi
    storageClassName: "gcp-ssd"
    
  clickhouse:
    enabled: true
    storageSize: 500Gi  # Larger storage for production
    storageClassName: "gcp-ssd"
    
  env:
    # Application Variables
    NEXT_PUBLIC_APP_NAME: "Langtrace - Production"
    NEXT_PUBLIC_ENVIRONMENT: "production"
    NEXT_PUBLIC_HOST: "https://langtrace.gcp-prd.inspectorio.com"
    NEXTAUTH_SECRET: "prd-langtrace-secret-change-me"
    
    # Database credentials - should be moved to secrets
    postgres_user: "ltuser"
    postgres_password: "ltpasswd-prd"
    postgres_database: "langtrace"
    
    # Clickhouse credentials - should be moved to secrets
    CLICK_HOUSE_USER: "lt_clickhouse_user"
    CLICK_HOUSE_PASSWORD: "clickhousepw-prd"
    CLICK_HOUSE_DATABASE_NAME: "langtrace_traces"
    
    # Admin credentials - should be moved to secrets
    ADMIN_EMAIL: "<EMAIL>"
    ADMIN_PASSWORD: "admin-password-change-me"
    NEXT_PUBLIC_ENABLE_ADMIN_LOGIN: "false"  # Disable admin login in production
    
    # Azure AD Variables (configure with actual values for production)
    AZURE_AD_CLIENT_ID: ""
    AZURE_AD_CLIENT_SECRET: ""
    AZURE_AD_TENANT_ID: ""

langtrace-common:
  enabled: true
