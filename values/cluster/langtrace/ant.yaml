# Langtrace configuration for ANT environment

istio-ingress:
  enabled: true
  istio:
    ingress:
      langtrace:
        enabled: true
        gateways:
          - common
        hosts:
          - "langtrace.gcp-ant.inspectorio.com"
        http:
          - route:
              - destination:
                  host: langtrace-svc.default.svc.cluster.local
                  port:
                    number: 3000

langtrace:
  langtraceApp:
    name: langtrace
    image: "scale3labs/langtrace-client"
    langtrace_release: "latest"
    replicaCount: 1
    
  postgres:
    enabled: true
    storageSize: 20Gi
    storageClassName: "gcp-ssd"
    
  clickhouse:
    enabled: true
    storageSize: 20Gi
    storageClassName: "gcp-ssd"
    
  env:
    # Application Variables
    NEXT_PUBLIC_APP_NAME: "Langtrace - ANT"
    NEXT_PUBLIC_ENVIRONMENT: "development"
    NEXT_PUBLIC_HOST: "https://langtrace.gcp-ant.inspectorio.com"
    NEXTAUTH_SECRET: "ant-langtrace-secret-change-me"
    
    # Database credentials - should be moved to secrets
    postgres_user: "ltuser"
    postgres_password: "ltpasswd-ant"
    postgres_database: "langtrace"
    
    # Clickhouse credentials - should be moved to secrets
    CLICK_HOUSE_USER: "lt_clickhouse_user"
    CLICK_HOUSE_PASSWORD: "clickhousepw-ant"
    CLICK_HOUSE_DATABASE_NAME: "langtrace_traces"
    
    # Admin credentials - should be moved to secrets
    ADMIN_EMAIL: "<EMAIL>"
    ADMIN_PASSWORD: "admin-password-change-me"
    NEXT_PUBLIC_ENABLE_ADMIN_LOGIN: "true"
    
    # Azure AD Variables (configure as needed)
    AZURE_AD_CLIENT_ID: ""
    AZURE_AD_CLIENT_SECRET: ""
    AZURE_AD_TENANT_ID: ""

langtrace-common:
  enabled: true
