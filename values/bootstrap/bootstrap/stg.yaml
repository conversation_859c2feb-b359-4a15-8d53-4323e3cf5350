namespaceArgocd: argocd
cluster: ant #cluster to deploy argo crd's
targetCluster: stg # cluster to deploy environments
envState: stg #environment name on a targetCluster
gitlabUrl: https://gitlab.inspectorio.com
repoUrl: &gitops **************************:devops/gitops-cd.git
monoRepoUrl: &monorepo https://gitlab.inspectorio.com/saas/monorepo.git
featureDomainSuffux: .preview.stg.inspectorio.com
featureDomainSecretName: inspectorio-com-common-tls
clusterBootstrapValues: ../../../../../envs/stg/values.yaml
productOrgValues:
  - "../../../../../envs/org/css.yaml"
  - "../../../../../envs/org/dna.yaml"
  - "../../../../../envs/org/infra.yaml"
  - "../../../../../envs/org/platform.yaml"
  - "../../../../../envs/org/pm.yaml"
  - "../../../../../envs/org/qrm.yaml"
  - "../../../../../envs/org/rsc.yaml"
gitlabTokenSecretname: argocd-gitlab-token
defaultDeliveryOption: manual

syncPolicy:
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    #allowEmpty: false # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 3m # the maximum amount of time allowed for the backoff strategy

previewSyncPolicy:
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    allowEmpty: true # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 15m # the maximum amount of time allowed for the backoff strategy

release_channels:
  dev: on_success
  rc: manual
  stable: manual
  preview: on_success

projects:
  stg:
    settings:
      criticality: high
    apps:
      bootstrap:
        meta: true
        syncWave: 1
        overwriteNamespace: argocd
        targetClusterOverride: ant

      baseline:
        syncWave: 1
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: default

  gitlab:
    apps:
      gitlab-runner:
        syncWave: 1
        overwriteNamespace: gitlab-runner

  devops:
    notification:
      enabled: false
    additional_namespaces:
      - kube-system
      - istio-system
      - kafka
      - product-risk
      - default
      - kafka-connect-cdc
      - remote-config
      - mobileresponder
      - passport
      - defect-recommend-be
      - car
      - tracking
      - sight-be
      - qa-platform
      - factory-risk-be
      - zendesk
      - clickhouse-ds
      - dm
      - subscription
      - rs
    apps:
      istio:
        syncWave: 1
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: istio-system
        disableClusterBootstrapValues: true
        ignoreDifferencesAdditional:
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      redis-operator:
        syncWave: 3
      alerting:
        syncWave: 15
      pgbouncer:
        syncWave: 5
        overwriteNamespace: pgbouncer
      postgres-operator:
        syncWave: 5
        overwriteNamespace: postgres-operator
      eck-operator:
        syncWave: 3
        overwriteNamespace: eck-operator
      clickhouse-operator:
        syncWave: 12
        overwriteNamespace: clickhouse-operator
        ignoreDifferencesAdditional:
          - group: "apiextensions.k8s.io"
            kind: CustomResourceDefinition
            jqPathExpressions:
              - .spec.versions[].additionalPrinterColumns[].priority
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      eck-sight:
        syncWave: 14
        overwriteNamespace: eck-sight
      eck-integration:
        syncWave: 14
        overwriteNamespace: eck-integration
      eck-infra:
        syncWave: 14
        overwriteNamespace: eck-infra
        syncPolicy:
          syncOptions:
            - Validate=true
            - PruneLast=true
            - ServerSideApply=true
      eck-datasync:
        syncWave: 14
        overwriteNamespace: eck-datasync
      kafka-operator:
        syncWave: 3
        overwriteNamespace: kafka-operator
      kafka:
        syncWave: 10
        overwriteNamespace: kafka
      kafka-schema-registry:
        syncWave: 11
        overwriteNamespace: kafka-schema-registry
      certificates:
        syncWave: 1
        overwriteNamespace: cert-manager
      pomerium:
        syncWave: 2
        overwriteNamespace: pomerium
      external-secrets:
        syncWave: 1
        overwriteNamespace: external-secrets
      kong:
        syncWave: 3
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: kong
      kong-gateway:
        syncWave: 3
        overwriteNamespace: kong
        preview:
          enabled: true
          helm:
            parameters:
              - name: ingress-api.ingressController.ingressClass
                value: "kong-ingress-api-pr-{{number}}"
              - name: ingress-integration-controlplane.ingressController.ingressClass
                value: "ingress-integration-pr-{{number}}"
              - name: ingress-integration-controlplane.ingressController.env.publish_service
                value: "kong/pr-kong-gateway-devops-stg-{{number}}-ingress-integration-proxy"
          gitlabProjectId: 769
        ignoreDifferencesAdditional:
          - group: ""
            kind: Service
            namespace: kong
            jqPathExpressions:
              - .spec.ports[].nodePort
          - kind: Secret
            name: kong-gateway-ingress-api-validation-webhook-keypair
            namespace: kong
            jsonPointers:
              - /data
          - kind: Secret
            name: kong-gateway-ingress-api-validation-webhook-ca-keypair
            namespace: kong
            jsonPointers:
              - /data
      patch-operator:
        syncWave: 4
        overwriteNamespace: patch-operator
      monitoring:
        syncWave: 10
        overwriteNamespace: monitoring
      datadog:
        syncWave: 6
        overwriteNamespace: datadog
        syncPolicy:
          syncOptions:
            - Validate=true
            - PruneLast=true
            - ServerSideApply=true
      kafka-ui:
        syncWave: 7
        overwriteNamespace: kafka-ui
      redis-ui:
        syncWave: 8
        overwriteNamespace: redis-ui
        syncPolicy:
          syncOptions:
            - Validate=true
            - PruneLast=true
            - ServerSideApply=true
      victoria-metrics:
        syncWave: 10
        overwriteNamespace: victoria-metrics
        ignoreDifferencesAdditional:
          - group: "apps"
            kind: StatefulSet
            jqPathExpressions:
              - .spec.volumeClaimTemplates[].apiVersion
              - .spec.volumeClaimTemplates[].kind
      gatekeeper:
        syncWave: 6
        overwriteNamespace: gatekeeper
      fluentd:
        syncWave: 10
        overwriteNamespace: fluentd
        syncPolicy:
          syncOptions:
            - Validate=true
            - PruneLast=true
            - ServerSideApply=true
      gcs-files-upload:
        syncWave: 10
        overwriteNamespace: gcs-files-upload
      imageproxy:
        syncWave: 11
        overwriteNamespace: imageproxy
      memcached-admin:
        syncWave: 11
        overwriteNamespace: memcached-admin
      gcp-notify:
        syncWave: 12
      keda:
        syncWave: 13
        overwriteNamespace: keda
        ignoreDifferencesAdditional:
          # https://github.com/kedacore/keda/issues/4732
          - group: apiregistration.k8s.io
            kind: APIService
            name: v1beta1.external.metrics.k8s.io
            jsonPointers:
              - /spec/insecureSkipTLSVerify
      devops-exporter:
        syncWave: 12
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          gitlabProjectId: 872
      sample-service:
        syncWave: 20
        overwriteNamespace: sample-service
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: sample-service.istio.ingress.default.hosts[0]
                value: "sample-service-{{number}}.preview.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "sample-service-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: global.infra.settings.kongIngress.integration.hosts[0].domains[0]
                value: "sample-service-{{number}}.preview-integration.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.integration.hosts[0].tlsSecretName
                value: preview-cert-tls
          gitlabProjectId: 843
      **********************webhook:
        syncWave: 1
        overwriteNamespace: pagerduty-integration
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: **********************webhook.istio.ingress.default.hosts[0]
                value: "**********************webhook-{{number}}.preview.stg.inspectorio.com"
              - name: **********************webhook.environmentFrom[0].secretRef.name
                value: "**********************webhook-{{number}}"
              - name: **********************webhook.externalSecretStore.**********************webhook.target
                value: "**********************webhook-{{number}}"
          gitlabProjectId: 877
      alerts-integration-webhook:
        syncWave: 1
        overwriteNamespace: alerts-integration-webhook
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: alerts-integration-webhook.istio.ingress.default.hosts[0]
                value: "alerts-integration-webhook-{{number}}.preview.stg.inspectorio.com"
              - name: alerts-integration-webhook.environmentFrom[0].secretRef.name
                value: "alerts-integration-webhook-{{number}}"
              - name: alerts-integration-webhook.externalSecretStore.alerts-integration-webhook.target
                value: "alerts-integration-webhook-{{number}}"
          gitlabProjectId: 877
      mrnotifybot:
        syncWave: 14
        overwriteNamespace: mrnotifybot
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: mrnotifybot.istio.ingress.default.hosts[0]
                value: "mrnotifybot-{{number}}.preview.stg.inspectorio.com"
              - name: mrnotifybot.environmentFrom[0].secretRef.name
                value: "mrnotifybot-{{number}}"
              - name: mrnotifybot.externalSecretStore.mrnotifybot-secrets.target
                value: "mrnotifybot-{{number}}"
          gitlabProjectId: 637
      infrabot:
        syncWave: 13
        overwriteNamespace: infrabot
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: infrabot.environmentFrom[0].secretRef.name
                value: "infrabot-{{number}}"
              - name: infrabot.externalSecretStore.infrabot.target
                value: "infrabot-{{number}}"
          gitlabProjectId: 874
      devops-portal-fe:
        syncWave: 7
        overwriteNamespace: devops-portal
      devops-portal-be:
        syncWave: 8
        overwriteNamespace: devops-portal
        syncPolicy:
          syncOptions:
            - Validate=true
            - PruneLast=true
            - ServerSideApply=true
      elastic-ui:
        syncWave: 8
        overwriteNamespace: elastic-ui
      devpi-proxy:
        syncWave: 8
        overwriteNamespace: devpi-proxy
      network-speedtest:
        syncWave: 13
        overwriteNamespace: network-speedtest
        migrationNamingEnable: true

  core:
    additional_namespaces:
      - kafka
    apps:
      hermes-be:
        syncWave: 31
        overwriteNamespace: hermes
      portal:
        syncWave: 24
        overwriteNamespace: portal
        preview:
          enabled: true
          helm:
            parameters:
              - name: portal.istio.ingress.default.hosts[0]
                value: "portal-{{number}}.preview.stg.inspectorio.com"
          gitlabProjectId: 24
      kong-auth-router:
        syncWave: 32
        overwriteNamespace: default
      hermes-fe:
        syncWave: 33
        overwriteNamespace: hermes
        preview:
          enabled: true
          helm:
            parameters:
              - name: hermes-fe.istio.ingress.default.hosts[0]
                value: "id-{{number}}.preview.stg.inspectorio.com"
          gitlabProjectId: 684
      passport-be:
        syncWave: 34
        overwriteNamespace: passport
        preview:
          enabled: true
          helm:
            parameters:
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "passport-be-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: passport-be.environmentFrom[0].secretRef.name
                value: "core-passport-be-{{number}}"
              - name: passport-be.externalSecretStore.passport-be.target
                value: "core-passport-be-{{number}}"
          gitlabProjectId: 574
      bouncers:
        syncWave: 35
        overwriteNamespace: bouncers
      fms:
        syncWave: 36
        overwriteNamespace: fms
      permission-dashboard:
        syncWave: 37
        overwriteNamespace: permission-dashboard
      sms:
        syncWave: 37
        overwriteNamespace: sms
      chatbot-be:
        syncWave: 38
        overwriteNamespace: chatbot-be
        syncPolicy:
          syncOptions:
            - Validate=true
            - PruneLast=true
            - ServerSideApply=true
      dev-portal:
        syncWave: 39
        overwriteNamespace: dev-portal

  # New structure product naming parts: pm, rsc, qrm, css, dna, sec, infra
  infra:
    notification:
      enabled: false
    additional_namespaces:
      - kube-system
      - istio-system
      - kafka
      - product-risk
      - default
      - kafka-connect-cdc
      - remote-config
      - mobileresponder
      - passport
      - defect-recommend-be
      - car
      - tracking
      - sight-be
      - qa-platform
      - factory-risk-be
      - zendesk
      - clickhouse-ds
      - dm
      - subscription
      - rs
    apps:
      cloudflare-tunnel:
        syncWave: 10
        overwriteNamespace: cloudflare-tunnel
        migrationNamingEnable: true

  sec:
    apps:
      h1way:
        syncWave: 30
        overwriteNamespace: h1way
      jimmy:
        syncWave: 13
        overwriteNamespace: jimmy
        migrationNamingEnable: true

  css:
    apps:
      org-resolver:
        syncWave: 30
        overwriteNamespace: org-resolver
        migrationNamingEnable: true
      subscription-fe:
        syncWave: 30
        overwriteNamespace: subscription
        migrationNamingEnable: true
      subscription-be:
        syncWave: 30
        overwriteNamespace: subscription
        migrationNamingEnable: true

  dna: # data and analytics
    additional_namespaces:
      - kafka
    apps:
      analytics-superset:
        syncWave: 10
        migrationNamingEnable: true
        syncPolicy:
          automated:
            prune: true
            selfHeal: true
            allowEmpty: false
          syncOptions:
            - Validate=true
            - CreateNamespace=false
            - PrunePropagationPolicy=foreground
            - PruneLast=true
            - ServerSideApply=false
        overwriteNamespace: superset
      apache-superset:
        syncWave: 10
        migrationNamingEnable: true
        syncPolicy:
          automated:
            prune: true
            selfHeal: true
            allowEmpty: false
          syncOptions:
            - Validate=true
            - CreateNamespace=false
            - PrunePropagationPolicy=foreground
            - PruneLast=true
            - ServerSideApply=false
        overwriteNamespace: superset
      product-risk-be:
        syncWave: 50
        migrationNamingEnable: true
        overwriteNamespace: product-risk
      pub-capa:
        syncWave: 52
        overwriteNamespace: pub-capa
        migrationNamingEnable: true
      clickhouse-ds:
        syncWave: 10
        overwriteNamespace: clickhouse-ds
        migrationNamingEnable: true
      kafka-connect-cdc:
        syncWave: 10
        overwriteNamespace: kafka-connect-cdc
        migrationNamingEnable: true
      defect-recommend-be:
        syncWave: 10
        overwriteNamespace: defect-recommend-be
        migrationNamingEnable: true
      airflow:
        syncWave: 50
        overwriteNamespace: airflow
        migrationNamingEnable: true
      qa-platform:
        syncWave: 50
        overwriteNamespace: qa-platform
        migrationNamingEnable: true
      factory-risk-be:
        syncWave: 51
        overwriteNamespace: factory-risk-be
        migrationNamingEnable: true
      timing-formula:
        syncWave: 50
        overwriteNamespace: timing-formula
        migrationNamingEnable: true
      dedupe-api:
        syncWave: 50
        overwriteNamespace: dedupe-api
        migrationNamingEnable: true
      analytic3:
        syncWave: 50
        overwriteNamespace: analytic3
        migrationNamingEnable: true
        preview:
          enabled: true
          gitlabProjectId: 148
          helm:
            parameters:
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "analytic3-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: analytic3.externalSecretStore.main.target
                value: pr-analytic3-ds-stg-{{number}}-main
              - name: analytic3.environmentFrom[0].secretRef.name
                value: pr-analytic3-ds-stg-{{number}}-main
      document-validator:
        syncWave: 50
        overwriteNamespace: document-validator
        migrationNamingEnable: true

  pm:
    additional_namespaces:
      - kafka
    apps:
      tracking:
        syncWave: 90
        overwriteNamespace: tracking
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "tracking-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: tracking.environmentFrom[0].secretRef.name
                value: "lst-tracking-{{number}}"
              - name: tracking.externalSecretStore.lst-tracking.target
                value: "lst-tracking-{{number}}"
          gitlabProjectId: 795

  rsc:
    additional_namespaces:
      - kafka
    apps:
      userssync:
        syncWave: 34
        overwriteNamespace: userssync
        migrationNamingEnable: true
      translation:
        syncWave: 35
        overwriteNamespace: translation
        migrationNamingEnable: true
      rs-backend:
        syncWave: 36
        overwriteNamespace: rs
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: rs-backend.istio.ingress.default.hosts[0]
                value: "rs-backend-{{number}}.preview.stg.inspectorio.com"
              - name: "rs-backend.environment.POSTGRES_HOST"
                value: "pr-rs-backend-stg-{{number}}-postgresql"
              - name: "rs-backend.jobs.migration.initContainers.seed-db.environment.PGREST_HOST"
                value: "pr-rs-backend-stg-{{number}}-postgresql"
            valuesObject:
              postgresql:
                primary:
                  persistence:
                    labels:
                      argocd.argoproj.io/instance: pr-rs-backend-stg-{{number}}
          gitlabProjectId: 124
      rise-integration-api:
        syncWave: 37
        overwriteNamespace: rs
        migrationNamingEnable: true
      rs-frontend:
        syncWave: 38
        overwriteNamespace: rs
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: rs-frontend.istio.ingress.default.hosts[0]
                value: "rs-frontend-{{number}}.preview.stg.inspectorio.com"
          gitlabProjectId: 125
      collabora-online:
        syncWave: 2
        migrationNamingEnable: true
        overwriteNamespace: dm
      dm-backend:
        syncWave: 20
        migrationNamingEnable: true
        overwriteNamespace: dm
        ignoreDifferencesAdditional:
          - group: "redis.redis.opstreelabs.in"
            kind: RedisReplication
            jqPathExpressions:
              - .spec.storage.volumeClaimTemplate.metadata.name
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: dm-backend.istio.ingress.default.hosts[0]
                value: "dm-backend-{{number}}.preview.stg.inspectorio.com"
              - name: dm-backend.environmentFrom[0].secretRef.name
                value: "dm-dm-backend-{{number}}"
              - name: dm-backend.externalSecretStore.dm-backend.target
                value: "dm-dm-backend-{{number}}"
          gitlabProjectId: 632
      dm-frontend:
        syncWave: 11
        migrationNamingEnable: true
        overwriteNamespace: dm
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          helm:
            parameters:
              - name: dm-frontend.istio.ingress.default.hosts[0]
                value: "dm-frontend-{{number}}.preview.stg.inspectorio.com"
              - name: dm-frontend.environmentFrom[0].secretRef.name
                value: "dm-dm-frontend-{{number}}"
              - name: dm-frontend.externalSecretStore.dm-frontend.target
                value: "dm-dm-frontend-{{number}}"
          gitlabProjectId: 633
      dm-metrics:
        syncWave: 20
        migrationNamingEnable: true
        overwriteNamespace: dm
      thirdparty-reports:
        syncWave: 30
        overwriteNamespace: thirdparty-reports
        migrationNamingEnable: true

  qrm:
    additional_namespaces:
      - kafka
    apps:
      emlauncher-be:
        syncWave: 45
        overwriteNamespace: emlauncher
        migrationNamingEnable: true
      emlauncher-fe:
        syncWave: 45
        overwriteNamespace: emlauncher
        migrationNamingEnable: true
      master-data:
        syncWave: 41
        overwriteNamespace: master-data
        migrationNamingEnable: true
        preview:
          ignoreDifferencesAdditional:
            - group: "external-secrets.io"
              kind: "ExternalSecret"
          enabled: true
          gitlabProjectId: 608
      mobileresponder:
        syncWave: 44
        overwriteNamespace: mobileresponder
        migrationNamingEnable: true
      remote-config:
        syncWave: 43
        overwriteNamespace: remote-config
        migrationNamingEnable: true
      zendesk-sso:
        syncWave: 45
        overwriteNamespace: zendesk
        migrationNamingEnable: true
      sight-be:
        syncWave: 70
        overwriteNamespace: sight-be
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "sight-be-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: global.infra.settings.kongIngress.integration.hosts[0].domains[0]
                value: "sight-be-{{number}}.preview-integration.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.integration.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: sight-be.environmentFrom[0].secretRef.name
                value: "lst-sight-be-{{number}}"
              - name: sight-be.externalSecretStore.lst-sight-be.target
                value: "lst-sight-be-{{number}}"
          gitlabProjectId: 158
      frontend:
        syncWave: 81
        overwriteNamespace: frontend
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: frontend.istio.ingress.default.hosts[0]
                value: "frontend-{{number}}.preview.stg.inspectorio.com"
          gitlabProjectId: 14
      car:
        syncWave: 45
        overwriteNamespace: car
        migrationNamingEnable: true
        syncOptions:
          - Validate=true
          - CreateNamespace=true
          - PrunePropagationPolicy=orphan
          - PruneLast=true
          - ServerSideApply=true
        preview:
          enabled: true
          helm:
            parameters:
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "car-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: car.environmentFrom[0].secretRef.name
                value: "sight-car-{{number}}"
              - name: car.externalSecretStore.car-be.target
                value: "sight-car-{{number}}"
          gitlabProjectId: 12
      report-html:
        syncWave: 45
        overwriteNamespace: report-html
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: report-html.istio.ingress.default.hosts[0]
                value: "report-html-{{number}}.preview.stg.inspectorio.com"
          gitlabProjectId: 367
      notimanager:
        syncWave: 100
        overwriteNamespace: notimanager
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: global.infra.settings.kongIngress.api.hosts[0].domains[0]
                value: "notimanager-{{number}}.preview-api.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.api.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: notimanager.environmentFrom[0].secretRef.name
                value: "sight-notimanager-{{number}}"
              - name: notimanager.externalSecretStore.sight-notimanager.target
                value: "sight-notimanager-{{number}}"
          gitlabProjectId: 18
      integration-api:
        syncWave: 110
        overwriteNamespace: integration-api
        migrationNamingEnable: true
        preview:
          enabled: true
          helm:
            parameters:
              - name: integration-api.istio.ingress.default.hosts[0]
                value: "integration-api-{{number}}.preview.stg.inspectorio.com"
              - name: integration-api.environmentFrom[0].secretRef.name
                value: "sight-integration-api-{{number}}"
              - name: global.infra.settings.kongIngress.integration.hosts[0].domains[0]
                value: "integration-api-{{number}}.preview-integration.stg.inspectorio.com"
              - name: global.infra.settings.kongIngress.integration.hosts[0].tlsSecretName
                value: preview-cert-tls
              - name: integration-api.externalSecretStore.sight-integration-api.target
                value: "sight-integration-api-{{number}}"
          gitlabProjectId: 108
      mobile-slackbot:
        syncWave: 46
        overwriteNamespace: mobile-slackbot
        migrationNamingEnable: true
      ins-design-tokens:
        syncWave: 46
        overwriteNamespace: ins-design-tokens
        migrationNamingEnable: true

  integration:
    apps:
      sftpgo:
        syncWave: 2
        overwriteNamespace: sftpgo
