# Cluster-level overrides for Langtrace
# Environment-specific values should be placed in values/cluster/langtrace/

langtrace:
  # Override default values from generic chart here
  langtraceApp:
    # Use custom image repository if needed
    # image: "asia-docker.pkg.dev/inspectorio-ant/mirror/scale3labs/langtrace-client"
    
    # Cluster-specific settings
    imagePullPolicy: IfNotPresent
    
    # Ingress will be handled by <PERSON><PERSON><PERSON>, so disable default ingress
    ingress:
      create: false

  # Database configurations - these will be overridden per environment
  postgres:
    enabled: true
    # storageClassName: "gcp-ssd"  # Uncomment and set per environment
    
  clickhouse:
    enabled: true
    # storageClassName: "gcp-ssd"  # Uncomment and set per environment

# Common configurations that can be shared across environments
langtrace-common:
  enabled: true

# Istio ingress configuration - will be configured per environment
istio-ingress:
  enabled: false  # Will be enabled per environment with specific configurations
